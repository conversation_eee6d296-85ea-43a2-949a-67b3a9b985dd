"use client";

import { useState, useR<PERSON>, use<PERSON><PERSON><PERSON> } from "react";
import { useTranslations } from "next-intl";
import { motion, AnimatePresence } from "motion/react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { BackgroundBlurs } from "../components/BackgroundBlurs";
import { FullWidthHistoryGallery } from "./components/FullWidthHistoryGallery";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Loader2,
  Download,
  Trash2,
  Sparkles,
  Wand2,
  Image,
  Upload,
  Settings,
  Refresh<PERSON><PERSON>,
  <PERSON>,
  Undo2,
  Redo2,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>bulb,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>
} from "lucide-react";

// Types
interface HistoryItem {
  id: number;
  originalImage: string;
  resultImage: string;
  prompt: string;
  timestamp: string;
  settings?: ProcessingSettings;
}

interface ProcessingSettings {
  quality: number;
  preserveAspect: boolean;
  outputFormat: 'jpg' | 'png' | 'webp';
  enhanceDetails: boolean;
}

interface PromptSuggestion {
  id: string;
  text: string;
  category: 'enhance' | 'retouch' | 'background' | 'style' | 'creative';
  icon: React.ReactNode;
}

// Enhanced prompt suggestions
const promptSuggestions: PromptSuggestion[] = [
  { id: 'remove-bg', text: 'Remove background completely', category: 'background', icon: <Scissors className="h-4 w-4" /> },
  { id: 'sunset-bg', text: 'Change background to beautiful sunset', category: 'background', icon: <Palette className="h-4 w-4" /> },
  { id: 'enhance-quality', text: 'Enhance image quality and sharpness', category: 'enhance', icon: <Sparkles className="h-4 w-4" /> },
  { id: 'portrait-retouch', text: 'Professional portrait retouching', category: 'retouch', icon: <Wand2 className="h-4 w-4" /> },
  { id: 'artistic-style', text: 'Apply artistic painting style', category: 'style', icon: <Palette className="h-4 w-4" /> },
  { id: 'vintage-filter', text: 'Add vintage film effect', category: 'style', icon: <Image className="h-4 w-4" /> },
  { id: 'make-smile', text: 'Make the person smile naturally', category: 'retouch', icon: <Wand2 className="h-4 w-4" /> },
  { id: 'dramatic-lighting', text: 'Add dramatic lighting effects', category: 'enhance', icon: <Zap className="h-4 w-4" /> },
];

// Enhanced mock history data for demonstration
const mockHistory: HistoryItem[] = [
  {
    id: 1,
    originalImage: "https://picsum.photos/600/400?random=1",
    resultImage: "https://picsum.photos/600/400?random=2",
    prompt: "Transform this landscape into a magical fantasy scene with glowing elements and ethereal lighting",
    timestamp: "2024-03-20 14:30",
    settings: { quality: 95, preserveAspect: true, outputFormat: 'png', enhanceDetails: true }
  },
  {
    id: 2,
    originalImage: "https://picsum.photos/500/600?random=3",
    resultImage: "https://picsum.photos/500/600?random=4",
    prompt: "Enhance portrait quality, remove blemishes, and add professional studio lighting",
    timestamp: "2024-03-20 13:15",
    settings: { quality: 98, preserveAspect: true, outputFormat: 'jpg', enhanceDetails: true }
  },
  {
    id: 3,
    originalImage: "https://picsum.photos/800/500?random=5",
    resultImage: "https://picsum.photos/800/500?random=6",
    prompt: "Change background to a beautiful sunset beach scene with palm trees",
    timestamp: "2024-03-20 11:45",
    settings: { quality: 90, preserveAspect: true, outputFormat: 'webp', enhanceDetails: true }
  },
  {
    id: 4,
    originalImage: "https://picsum.photos/400/600?random=7",
    resultImage: "https://picsum.photos/400/600?random=8",
    prompt: "Apply vintage film effect with warm tones and subtle grain",
    timestamp: "2024-03-20 10:20",
    settings: { quality: 85, preserveAspect: true, outputFormat: 'jpg', enhanceDetails: false }
  },
  {
    id: 5,
    originalImage: "https://picsum.photos/700/400?random=9",
    resultImage: "https://picsum.photos/700/400?random=10",
    prompt: "Convert to black and white with dramatic contrast and lighting",
    timestamp: "2024-03-20 09:15",
    settings: { quality: 92, preserveAspect: true, outputFormat: 'png', enhanceDetails: true }
  },
  {
    id: 6,
    originalImage: "https://picsum.photos/500/500?random=11",
    resultImage: "https://picsum.photos/500/500?random=12",
    prompt: "Add artistic watercolor painting style with soft brush strokes",
    timestamp: "2024-03-19 16:45",
    settings: { quality: 88, preserveAspect: false, outputFormat: 'jpg', enhanceDetails: true }
  },
  {
    id: 7,
    originalImage: "https://picsum.photos/600/500?random=13",
    resultImage: "https://picsum.photos/600/500?random=14",
    prompt: "Remove all people from the scene and fill with natural elements",
    timestamp: "2024-03-19 15:30",
    settings: { quality: 94, preserveAspect: true, outputFormat: 'png', enhanceDetails: true }
  },
  {
    id: 8,
    originalImage: "https://picsum.photos/450/600?random=15",
    resultImage: "https://picsum.photos/450/600?random=16",
    prompt: "Make the person smile naturally and brighten their eyes",
    timestamp: "2024-03-19 14:20",
    settings: { quality: 96, preserveAspect: true, outputFormat: 'jpg', enhanceDetails: true }
  }
];

export default function Generate() {
  const t = useTranslations('Generate');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Enhanced state management
  const [prompt, setPrompt] = useState<string>('');
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [uploadedFileName, setUploadedFileName] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [processingProgress, setProcessingProgress] = useState<number>(0);
  const [result, setResult] = useState<string | null>(null);
  const [activeFeature, setActiveFeature] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [history, setHistory] = useState<HistoryItem[]>(mockHistory);
  const [showComparison, setShowComparison] = useState<boolean>(false);
  const [showSuggestions, setShowSuggestions] = useState<boolean>(true);
  const [selectedSuggestion, setSelectedSuggestion] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Processing settings
  const [settings, setSettings] = useState<ProcessingSettings>({
    quality: 90,
    preserveAspect: true,
    outputFormat: 'jpg',
    enhanceDetails: true,
  });

  // Undo/Redo state
  const [undoStack, setUndoStack] = useState<string[]>([]);
  const [redoStack, setRedoStack] = useState<string[]>([]);

  // Batch processing
  const [batchImages, setBatchImages] = useState<string[]>([]);
  const [batchMode, setBatchMode] = useState<boolean>(false);
  
  // Enhanced image processing with progress tracking
  const processImage = useCallback(async () => {
    if (!uploadedImage || !prompt.trim()) {
      setError('Please upload an image and enter a prompt');
      return;
    }

    setIsProcessing(true);
    setResult(null);
    setError(null);
    setProcessingProgress(0);

    try {
      // Simulate processing with progress updates
      const progressInterval = setInterval(() => {
        setProcessingProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + Math.random() * 15;
        });
      }, 200);

      // Add to undo stack before processing
      if (result) {
        setUndoStack(prev => [...prev, result]);
        setRedoStack([]); // Clear redo stack when new action is performed
      }

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      clearInterval(progressInterval);
      setProcessingProgress(100);

      // Simulate processing result
      const processedImage = uploadedImage; // In real app, this would be the processed image
      setResult(processedImage);

      // Add to history with settings
      const newHistoryItem: HistoryItem = {
        id: Date.now(),
        originalImage: uploadedImage,
        resultImage: processedImage,
        prompt: prompt,
        timestamp: new Date().toLocaleString(),
        settings: { ...settings }
      };
      setHistory(prev => [newHistoryItem, ...prev]);

      // Reset progress after a short delay
      setTimeout(() => setProcessingProgress(0), 1000);

    } catch (error) {
      setError('Failed to process image. Please try again.');
      console.error('Processing error:', error);
    } finally {
      setIsProcessing(false);
    }
  }, [uploadedImage, prompt, result, settings, history]);
  
  // Enhanced image upload with validation and multiple format support
  const handleImageUpload = useCallback((file: File) => {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
    if (!allowedTypes.includes(file.type)) {
      setError('Please upload a valid image file (JPG, PNG, WEBP, or GIF)');
      return;
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      setError('File size must be less than 10MB');
      return;
    }

    setError(null);
    setUploadedFileName(file.name);

    const reader = new FileReader();
    reader.onload = (event: ProgressEvent<FileReader>) => {
      if (event.target?.result) {
        setUploadedImage(event.target.result as string);
        setResult(null); // Reset result
        setShowSuggestions(true); // Show suggestions when new image is uploaded
      }
    };
    reader.onerror = () => {
      setError('Failed to read the image file');
    };
    reader.readAsDataURL(file);
  }, []);

  // Handle file input change
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleImageUpload(file);
    }
  };
  
  // Enhanced drag and drop with multiple file support
  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    // Only set dragging to false if we're leaving the drop zone entirely
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setIsDragging(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const files = Array.from(e.dataTransfer.files);
    const imageFiles = files.filter(file => file.type.startsWith('image/'));

    if (imageFiles.length === 0) {
      setError('Please drop valid image files');
      return;
    }

    if (batchMode && imageFiles.length > 1) {
      // Handle multiple files for batch processing
      const filePromises = imageFiles.slice(0, 5).map(file => {
        return new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = (event) => {
            if (event.target?.result) {
              resolve(event.target.result as string);
            } else {
              reject(new Error('Failed to read file'));
            }
          };
          reader.onerror = () => reject(new Error('Failed to read file'));
          reader.readAsDataURL(file);
        });
      });

      Promise.all(filePromises)
        .then(images => {
          setBatchImages(images);
          setUploadedImage(images[0] || null); // Set first image as primary
        })
        .catch(() => {
          setError('Failed to process some images');
        });
    } else {
      // Handle single file
      if (imageFiles[0]) {
        handleImageUpload(imageFiles[0]);
      }
    }
  }, [batchMode, handleImageUpload]);

  // Utility functions
  const resetToOriginal = useCallback(() => {
    if (uploadedImage) {
      setResult(uploadedImage);
      setUndoStack(prev => [...prev, result || '']);
      setRedoStack([]);
    }
  }, [uploadedImage, result]);

  const handleUndo = useCallback(() => {
    if (undoStack.length > 0 && result) {
      const previousState = undoStack[undoStack.length - 1];
      if (previousState) {
        setRedoStack(prev => [...prev, result]);
        setUndoStack(prev => prev.slice(0, -1));
        setResult(previousState);
      }
    }
  }, [undoStack, result]);

  const handleRedo = useCallback(() => {
    if (redoStack.length > 0) {
      const nextState = redoStack[redoStack.length - 1];
      if (nextState) {
        setUndoStack(prev => [...prev, result ?? '']);
        setRedoStack(prev => prev.slice(0, -1));
        setResult(nextState);
      }
    }
  }, [redoStack, result]);

  const downloadImage = useCallback((imageUrl: string, filename: string = 'edited-image') => {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = `${filename}.${settings.outputFormat}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, [settings.outputFormat]);

  // Enhanced feature handling with suggestions
  const handleFeatureClick = useCallback((feature: string) => {
    setActiveFeature(feature);
    setPrompt(t(`features.${feature}.template`));
    setSelectedSuggestion(null);
  }, [t]);

  const handleSuggestionClick = useCallback((suggestion: PromptSuggestion) => {
    setPrompt(suggestion.text);
    setSelectedSuggestion(suggestion.id);
    setActiveFeature(suggestion.category);
  }, []);
  
  // 功能列表
  const features = [
    { id: 'enhance', icon: <Sparkles className="h-4 w-4" /> },
    { id: 'retouch', icon: <Wand2 className="h-4 w-4" /> },
    { id: 'background', icon: <Image className="h-4 w-4" /> },
    { id: 'style', icon: <Settings className="h-4 w-4" /> },
    { id: 'resize', icon: <RefreshCw className="h-4 w-4" /> },
  ];
  
  return (
    <div className="min-h-screen w-full bg-gradient-to-b from-background to-background/80">
      <BackgroundBlurs />

      <div className="container mx-auto px-4 py-8">
        {/* Header Section */}
        <div className="text-center mb-8">
          <h2 className="text-2xl font-semibold text-foreground/90 mb-2">
            {t('title')}
          </h2>
          <p className="text-muted-foreground">
            上传图片并使用文本指令进行编辑
          </p>
        </div>

        {/* Primary Upload Interface - Always Visible */}
        <div className="max-w-6xl mx-auto space-y-8">
          {/* Enhanced editing area with tabs */}
          <Card className="bg-card/50 backdrop-blur-sm border-none">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-xl font-semibold">{t('title')}</CardTitle>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setBatchMode(!batchMode)}
                    className={batchMode ? 'bg-primary text-primary-foreground' : ''}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Batch Mode
                  </Button>
                  {error && (
                    <div className="flex items-center gap-2 text-destructive text-sm">
                      <AlertCircle className="h-4 w-4" />
                      {error}
                    </div>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Enhanced image upload area */}
              {!uploadedImage && !result ? (
                <motion.div
                  className={`border-2 border-dashed rounded-xl p-8 md:p-12 text-center w-full cursor-pointer transition-all ${
                    isDragging
                      ? 'border-primary bg-primary/10 scale-[1.02] shadow-lg'
                      : 'border-muted-foreground/25 hover:border-primary/50 hover:bg-primary/5'
                  }`}
                  whileHover={{ scale: 1.02 }}
                  onClick={() => fileInputRef.current?.click()}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                >
                  <div className="space-y-4">
                    <Upload className="h-12 w-12 md:h-16 md:w-16 mx-auto text-primary/60" />
                    <div>
                      <h3 className="text-lg md:text-xl font-medium mb-2">
                        {batchMode ? 'Upload Multiple Images' : t('image.upload')}
                      </h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        {batchMode
                          ? 'Drop up to 5 images for batch processing'
                          : 'Supports JPG, PNG, WEBP, GIF (max 10MB)'
                        }
                      </p>
                    </div>
                    <Button variant="outline" size="lg">
                      <Upload className="h-4 w-4 mr-2" />
                      {batchMode ? 'Select Images' : t('image.selectButton')}
                    </Button>
                  </div>
                  <input
                    type="file"
                    ref={fileInputRef}
                    className="hidden"
                    accept="image/*"
                    multiple={batchMode}
                    onChange={handleFileInputChange}
                  />
                </motion.div>
              ) : (
                <div className="w-full space-y-6">
                  {/* Enhanced image display with comparison view */}
                  <Tabs value={showComparison && result ? "comparison" : "single"} className="w-full">
                    <div className="flex items-center justify-between mb-4">
                      <TabsList className="grid w-fit grid-cols-2">
                        <TabsTrigger
                          value="single"
                          onClick={() => setShowComparison(false)}
                          className="flex items-center gap-2"
                        >
                          <Eye className="h-4 w-4" />
                          Single View
                        </TabsTrigger>
                        <TabsTrigger
                          value="comparison"
                          onClick={() => setShowComparison(true)}
                          disabled={!result}
                          className="flex items-center gap-2"
                        >
                          <Eye className="h-4 w-4" />
                          Compare
                        </TabsTrigger>
                      </TabsList>

                      <div className="flex items-center gap-2">
                        {result && (
                          <>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={handleUndo}
                              disabled={undoStack.length === 0}
                            >
                              <Undo2 className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={handleRedo}
                              disabled={redoStack.length === 0}
                            >
                              <Redo2 className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => downloadImage(result, 'edited-image')}
                            >
                              <Download className="h-4 w-4 mr-2" />
                              Download
                            </Button>
                          </>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setUploadedImage(null);
                            setResult(null);
                            setBatchImages([]);
                            setError(null);
                          }}
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Clear
                        </Button>
                      </div>
                    </div>

                    <TabsContent value="single" className="space-y-4">
                      {/* Single image view */}
                      <motion.div
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className="rounded-xl overflow-hidden bg-muted/20 relative"
                      >
                        <img
                          src={result || uploadedImage || ''}
                          alt={result ? "Edited image" : "Original image"}
                          className="w-full object-contain max-h-[50vh]"
                        />
                        <div className="absolute top-2 left-2 bg-black/50 text-white px-2 py-1 rounded text-xs">
                          {result ? 'Edited' : 'Original'}
                        </div>
                      </motion.div>
                    </TabsContent>

                    <TabsContent value="comparison" className="space-y-4">
                      {/* Before/After comparison */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <motion.div
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          className="rounded-xl overflow-hidden bg-muted/20 relative"
                        >
                          <img
                            src={uploadedImage || ''}
                            alt="Original image"
                            className="w-full object-contain max-h-[40vh]"
                          />
                          <div className="absolute top-2 left-2 bg-black/50 text-white px-2 py-1 rounded text-xs">
                            Before
                          </div>
                        </motion.div>

                        <motion.div
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          className="rounded-xl overflow-hidden bg-muted/20 relative"
                        >
                          <img
                            src={result || uploadedImage || ''}
                            alt="Edited image"
                            className="w-full object-contain max-h-[40vh]"
                          />
                          <div className="absolute top-2 left-2 bg-black/50 text-white px-2 py-1 rounded text-xs">
                            After
                          </div>
                        </motion.div>
                      </div>
                    </TabsContent>
                  </Tabs>
                </div>
              )}

              {/* Enhanced prompt and features area */}
              {uploadedImage && (
                <div className="space-y-6">
                  {/* Prompt suggestions */}
                  {showSuggestions && !result && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="space-y-3"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Lightbulb className="h-4 w-4 text-primary" />
                          <span className="text-sm font-medium">Quick Suggestions</span>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setShowSuggestions(false)}
                        >
                          <EyeOff className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-2">
                        {promptSuggestions.slice(0, 8).map((suggestion) => (
                          <motion.div
                            key={suggestion.id}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            <Button
                              variant={selectedSuggestion === suggestion.id ? "default" : "outline"}
                              size="sm"
                              className="w-full justify-start text-left h-auto p-3"
                              onClick={() => handleSuggestionClick(suggestion)}
                            >
                              <div className="flex items-start gap-2">
                                {suggestion.icon}
                                <span className="text-xs leading-tight">{suggestion.text}</span>
                              </div>
                            </Button>
                          </motion.div>
                        ))}
                      </div>
                    </motion.div>
                  )}

                  {/* Enhanced prompt input */}
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="prompt" className="text-sm font-medium">
                        Editing Instructions
                      </Label>
                      <div className="relative">
                        <Textarea
                          id="prompt"
                          placeholder={t('prompt.placeholder')}
                          value={prompt}
                          onChange={(e) => setPrompt(e.target.value)}
                          className="min-h-[80px] pr-12 resize-none"
                          disabled={isProcessing}
                        />
                        {!showSuggestions && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="absolute top-2 right-2"
                            onClick={() => setShowSuggestions(true)}
                          >
                            <Lightbulb className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>

                    {/* Processing progress */}
                    {isProcessing && (
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="space-y-2"
                      >
                        <div className="flex items-center justify-between text-sm">
                          <span>Processing your image...</span>
                          <span>{Math.round(processingProgress)}%</span>
                        </div>
                        <div className="w-full bg-muted rounded-full h-2">
                          <motion.div
                            className="bg-primary h-2 rounded-full"
                            initial={{ width: 0 }}
                            animate={{ width: `${processingProgress}%` }}
                            transition={{ duration: 0.3 }}
                          />
                        </div>
                      </motion.div>
                    )}

                    {/* Action buttons */}
                    <div className="flex flex-col sm:flex-row gap-3">
                      <Button
                        size="lg"
                        disabled={!prompt.trim() || isProcessing}
                        onClick={processImage}
                        className="flex-1"
                      >
                        {isProcessing ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            {t('processing')}
                          </>
                        ) : (
                          <>
                            <Wand2 className="mr-2 h-4 w-4" />
                            {t('generate')}
                          </>
                        )}
                      </Button>

                      {result && (
                        <Button
                          variant="outline"
                          size="lg"
                          onClick={resetToOriginal}
                        >
                          <RotateCcw className="mr-2 h-4 w-4" />
                          Reset
                        </Button>
                      )}
                    </div>
                  </div>

                  {/* Enhanced feature categories */}
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <Settings className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Quick Actions</span>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {features.map((feature) => (
                        <motion.div
                          key={feature.id}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <Button
                            variant={activeFeature === feature.id ? "default" : "outline"}
                            size="sm"
                            className="flex items-center gap-2"
                            onClick={() => handleFeatureClick(feature.id)}
                          >
                            {feature.icon}
                            <span>{t(`features.${feature.id}.name`)}</span>
                          </Button>
                        </motion.div>
                      ))}
                    </div>
                  </div>

                  {/* Advanced Settings Panel */}
                  <Card className="bg-muted/20 border-none">
                    <CardContent className="p-4 space-y-4">
                      <div className="flex items-center gap-2 mb-3">
                        <Settings className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm font-medium">Advanced Settings</span>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Quality Setting */}
                        <div className="space-y-2">
                          <Label htmlFor="quality" className="text-xs">
                            Output Quality: {settings.quality}%
                          </Label>
                          <Slider
                            id="quality"
                            min={50}
                            max={100}
                            step={5}
                            value={[settings.quality]}
                            onValueChange={(value) =>
                              setSettings(prev => ({ ...prev, quality: value[0] ?? 90 }))
                            }
                            className="w-full"
                          />
                        </div>

                        {/* Output Format */}
                        <div className="space-y-2">
                          <Label className="text-xs">Output Format</Label>
                          <div className="flex gap-2">
                            {(['jpg', 'png', 'webp'] as const).map((format) => (
                              <Button
                                key={format}
                                variant={settings.outputFormat === format ? "default" : "outline"}
                                size="sm"
                                onClick={() =>
                                  setSettings(prev => ({ ...prev, outputFormat: format }))
                                }
                                className="text-xs"
                              >
                                {format.toUpperCase()}
                              </Button>
                            ))}
                          </div>
                        </div>

                        {/* Preserve Aspect Ratio */}
                        <div className="flex items-center justify-between">
                          <Label htmlFor="preserve-aspect" className="text-xs">
                            Preserve Aspect Ratio
                          </Label>
                          <Switch
                            id="preserve-aspect"
                            checked={settings.preserveAspect}
                            onCheckedChange={(checked) =>
                              setSettings(prev => ({ ...prev, preserveAspect: checked }))
                            }
                          />
                        </div>

                        {/* Enhance Details */}
                        <div className="flex items-center justify-between">
                          <Label htmlFor="enhance-details" className="text-xs">
                            Enhance Details
                          </Label>
                          <Switch
                            id="enhance-details"
                            checked={settings.enhanceDetails}
                            onCheckedChange={(checked) =>
                              setSettings(prev => ({ ...prev, enhanceDetails: checked }))
                            }
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </CardContent>
          </Card>

          </div>

        {/* Full-Width History Gallery Section */}
        {history.length > 0 && (
          <div className="w-full mt-16">
            <FullWidthHistoryGallery
              history={history}
              setHistory={setHistory}
              downloadImage={downloadImage}
              setUploadedImage={setUploadedImage}
              setPrompt={setPrompt}
              setResult={setResult}
            />
          </div>
        )}
      </div>
    </div>
  );
}
